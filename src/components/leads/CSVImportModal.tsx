import React, { useState, useCallback } from 'react';
import { Download, Upload, FileText, ArrowRight, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CSVTemplateDownload } from './CSVTemplateDownload';
import { CSVFileUpload } from './CSVFileUpload';
import { toast } from 'sonner';

interface CSVImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFileSelect?: (file: File) => Promise<void>;
  isUploading?: boolean;
  uploadProgress?: number;
}

export const CSVImportModal: React.FC<CSVImportModalProps> = ({
  open,
  onOpenChange,
  onFileSelect,
  isUploading = false,
  uploadProgress = 0
}) => {
  const [activeTab, setActiveTab] = useState<'template' | 'upload'>('template');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  /**
   * Handles file selection from upload component
   */
  const handleFileSelect = useCallback(async (file: File) => {
    setSelectedFile(file);

    if (onFileSelect) {
      try {
        await onFileSelect(file);
        // Switch to upload tab to show progress
        setActiveTab('upload');
      } catch (error) {
        console.error('Error handling file selection:', error);
        toast.error('שגיאה בטעינת הקובץ');
      }
    }
  }, [onFileSelect]);

  /**
   * Handles tab change with validation
   */
  const handleTabChange = useCallback((value: string) => {
    if (value === 'template' || value === 'upload') {
      setActiveTab(value);
    }
  }, []);

  /**
   * Moves to upload tab after template download
   */
  const moveToUpload = useCallback(() => {
    setActiveTab('upload');
    toast.success('כעת ניתן להעלות את הקובץ שהורד');
  }, []);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            ייבוא לידים מקובץ CSV
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Process Steps Indicator */}
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
              activeTab === 'template' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
              <Download className="w-4 h-4" />
              <span className="text-sm font-medium">1. הורד תבנית</span>
            </div>
            <ArrowRight className="w-4 h-4 text-muted-foreground" />
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
              activeTab === 'upload' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
              <Upload className="w-4 h-4" />
              <span className="text-sm font-medium">2. העלה קובץ</span>
            </div>
          </div>

          <Separator />

          {/* Tab Content */}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="template" className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                הורדת תבנית
              </TabsTrigger>
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                העלאת קובץ
              </TabsTrigger>
            </TabsList>

            <TabsContent value="template" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">שלב 1: הורדת תבנית CSV</CardTitle>
                  <CardDescription>
                    הורד תבנית CSV מוכנה עם דוגמאות נתונים וכותרות מתאימות
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CSVTemplateDownload />

                  <Separator className="my-6" />

                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground">
                      לאחר הורדת התבנית, מלא את הנתונים ועבור לשלב הבא
                    </div>
                    <Button onClick={moveToUpload} className="flex items-center gap-2">
                      המשך לשלב הבא
                      <ArrowLeft className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="upload" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">שלב 2: העלאת קובץ CSV</CardTitle>
                  <CardDescription>
                    העלה את קובץ ה-CSV שמילאת עם נתוני הלידים
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CSVFileUpload
                    onFileSelect={handleFileSelect}
                    isUploading={isUploading}
                    uploadProgress={uploadProgress}
                    maxFileSize={5}
                  />

                  {selectedFile && (
                    <>
                      <Separator className="my-6" />

                      <div className="space-y-4">
                        <h4 className="font-medium">קובץ נבחר:</h4>
                        <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                          <FileText className="w-5 h-5 text-primary" />
                          <div>
                            <p className="font-medium text-sm">{selectedFile.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>

                        {!isUploading && (
                          <div className="flex justify-end">
                            <Button
                              onClick={() => handleFileSelect(selectedFile)}
                              className="flex items-center gap-2"
                            >
                              <Upload className="w-4 h-4" />
                              התחל ייבוא
                            </Button>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Quick Tips */}
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="text-base">טיפים לייבוא מוצלח</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>ודא שהקובץ שלך בפורמט CSV עם קידוד UTF-8</li>
                <li>השורה הראשונה חייבת להכיל כותרות עמודות</li>
                <li>שדות חובה: שם מלא וטלפון</li>
                <li>מספרי טלפון בפורמט ישראלי (050xxxxxxx או 02xxxxxxx)</li>
                <li>מקסימום 1000 לידים בייבוא אחד</li>
                <li>לידים כפולים (לפי טלפון) יידחו אוטומטית</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};